import os
import json
import requests
from dotenv import load_dotenv

load_dotenv()

AZURE_MODEL_ENDPOINT = os.getenv("AZURE_FOUNDRY_MODEL_ENDPOINT")
AZURE_ENDPOINT = os.getenv("AZURE_FOUNDRY_ENDPOINT")
AZURE_API_VERSION = "2025-04-01-preview"
AZURE_API_KEY = os.getenv("AZURE_FOUNDRY_API_KEY")

def enhance_segments_with_llm(segments: list[dict]) -> list[dict]:
    if not AZURE_MODEL_ENDPOINT or not AZURE_ENDPOINT or not AZURE_API_KEY:
        return segments
    # Send in constrained JSON and ask to return corrected text preserving
    # timestamps and speaker labels.
    prompt = (
        "You are enhancing ASR text. Fix casing, punctuation, and minor "
        "errors, but do not alter meaning. Preserve timestamps and speaker. "
        "Return JSON list with same fields."
    )
    payload = {
        "messages": [
            {"role": "system", "content": prompt},
            {
                "role": "user",
                "content": json.dumps(segments, ensure_ascii=False),
            },
        ],
        "temperature": 0.2, # Minimum recommended temperature for gpt-4.1 models
    }
    headers = {
        "Content-Type": "application/json",
        "api-key": AZURE_API_KEY,
    }
    if AZURE_MODEL_ENDPOINT:
        url = AZURE_MODEL_ENDPOINT
    else:
        url = f"{AZURE_ENDPOINT}/chat/completions?api-version={AZURE_API_VERSION}"
    r = requests.post(url, json=payload, headers=headers, timeout=120)
    r.raise_for_status()
    content = (
        r.json()
        .get("choices", [{}])[0]
        .get("message", {})
        .get("content", "[]")
    )
    try:
        enhanced = json.loads(content)
        if isinstance(enhanced, list):
            return enhanced
    except Exception:
        pass
    return segments