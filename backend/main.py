from fastapi import Fast<PERSON><PERSON>, UploadFile, File, WebSocket
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from uuid import uuid4
from pathlib import Path
import asyncio
import uvicorn

from workers.queue import enqueue_transcription
from storage.local_store import save_upload
from storage.models import get_job_status, get_job_artifacts

app = FastAPI()
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # tighten in prod
    allow_methods=["*"],
    allow_headers=["*"],
)

class TranscriptionRequest(BaseModel):
    diarization: bool = False
    diar_backend: str = "none"  # "none" | "nemo" | "pyannote"
    enhance_with_llm: bool = False
    language: str | None = None  # e.g., "en"
    model_size: str = "large-v3"  # or "distil-large-v3"
    output_formats: list[str] = ["srt", "vtt", "json"]

@app.post("/api/transcribe")
async def transcribe(
    req: TranscriptionRequest, file: UploadFile = File(...)
):
    job_id = str(uuid4())
    base = Path("data/uploads")
    base.mkdir(parents=True, exist_ok=True)
    file_path = await save_upload(base, job_id, file)
    await enqueue_transcription(
        job_id=job_id,
        file_path=str(file_path),
        diarization=req.diarization,
        diar_backend=req.diar_backend,
        enhance_with_llm=req.enhance_with_llm,
        language=req.language,
        model_size=req.model_size,
        output_formats=req.output_formats,
    )
    return {"job_id": job_id}

@app.get("/api/status/{job_id}")
def status(job_id: str):
    return get_job_status(job_id)

@app.get("/api/artifacts/{job_id}")
def artifacts(job_id: str):
    return get_job_artifacts(job_id)

@app.websocket("/ws/progress/{job_id}")
async def ws_progress(websocket: WebSocket, job_id: str):
    await websocket.accept()
    try:
        while True:
            status = get_job_status(job_id)
            await websocket.send_json(status)
            if status.get("state") in ["done", "error"]:
                break
            await asyncio.sleep(1.0)
    finally:
        await websocket.close()

if __name__ == "__main__":
    uvicorn.run("main:app", host="0.0.0.0", port=8080)
