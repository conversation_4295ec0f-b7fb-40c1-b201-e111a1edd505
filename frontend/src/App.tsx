import React, { useState } from "react";

function App() {
  const [file, setFile] = useState<File | null>(null);
  const [jobId, setJobId] = useState<string | null>(null);
  const [status, setStatus] = useState<any | null>(null);
  const [formats, setFormats] = useState<string[]>(["srt", "vtt", "json"]);
  const [enhance, setEnhance] = useState(false);
  const [diar, setDiar] = useState(false);
  const [diarBackend, setDiarBackend] = useState("none");
  const [model, setModel] = useState("large-v3");

  const onDrop = (e: React.DragEvent) => {
    e.preventDefault();
    if (e.dataTransfer.files?.length) {
      setFile(e.dataTransfer.files[0]);
    }
  };

  const start = async () => {
    if (!file) return;

    try {
      setStatus({ state: "uploading" });

      const fd = new FormData();
      fd.append("file", file);

      // FastAPI expects the JSON data as a form field named after the parameter
      fd.append("req", JSON.stringify({
        diarization: diar,
        diar_backend: diarBackend,
        enhance_with_llm: enhance,
        language: null,
        model_size: model,
        output_formats: formats,
      }));

      const res = await fetch("/api/transcribe", {
        method: "POST",
        body: fd,
      });

      if (!res.ok) {
        throw new Error(`HTTP error! status: ${res.status}`);
      }

      const data = await res.json();
      setJobId(data.job_id);

      const ws = new WebSocket(
        `${location.protocol === "https:" ? "wss" : "ws"}://${location.host
        }/ws/progress/${data.job_id}`
      );
      ws.onmessage = (ev) => setStatus(JSON.parse(ev.data));
      ws.onerror = (error) => {
        console.error("WebSocket error:", error);
        setStatus({ state: "error", error: "WebSocket connection failed" });
      };
    } catch (error) {
      console.error("Failed to start transcription:", error);
      setStatus({
        state: "error",
        error: error instanceof Error ? error.message : "Unknown error occurred"
      });
    }
  };

  const reset = () => {
    setFile(null);
    setJobId(null);
    setStatus(null);
  };

  const download = async () => {
    if (!jobId) return;
    try {
      const res = await fetch(`/api/artifacts/${jobId}`);
      const data = await res.json();

      for (const format of Object.keys(data)) {
        // Use the new download endpoint
        const a = document.createElement("a");
        a.href = `/api/download/${jobId}/${format}`;
        a.download = `transcript.${format}`;
        a.click();
      }
    } catch (error) {
      console.error("Failed to download artifacts:", error);
      alert("Failed to download files. Please check the console for details.");
    }
  };

  return (
    <div
      onDrop={onDrop}
      onDragOver={(e) => e.preventDefault()}
      style={{
        height: "100vh",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        flexDirection: "column",
        gap: 16,
      }}
    >
      <h2>Drag & Drop Transcription</h2>
      <input
        type="file"
        accept="video/*,audio/*"
        onChange={(e) => setFile(e.target.files?.[0] || null)}
      />
      {file && (
        <p style={{ color: "green" }}>
          Selected: {file.name} ({(file.size / 1024 / 1024).toFixed(2)} MB)
        </p>
      )}
      <div>
        <label>
          Model:
          <select
            value={model}
            onChange={(e) => setModel(e.target.value)}
            style={{ marginLeft: 8 }}
          >
            <option value="large-v3">Whisper large-v3</option>
            <option value="distil-large-v3">Distil-Whisper large-v3</option>
          </select>
        </label>
      </div>
      <label>
        <input
          type="checkbox"
          checked={diar}
          onChange={(e) => setDiar(e.target.checked)}
        />
        Diarization
      </label>
      {diar && (
        <div>
          <label>
            Diarization Backend:
            <select
              value={diarBackend}
              onChange={(e) => setDiarBackend(e.target.value)}
              style={{ marginLeft: 8 }}
            >
              <option value="none">None</option>
              <option value="nemo">NeMo</option>
              <option value="pyannote">Pyannote</option>
            </select>
          </label>
        </div>
      )}
      <label>
        <input
          type="checkbox"
          checked={enhance}
          onChange={(e) => setEnhance(e.target.checked)}
        />
        LLM Punctuation/Enhance
      </label>
      <div>
        <label>Output Formats:</label>
        <div style={{ display: "flex", gap: 16, marginTop: 8 }}>
          {["srt", "vtt", "json"].map((format) => (
            <label key={format}>
              <input
                type="checkbox"
                checked={formats.includes(format)}
                onChange={(e) => {
                  if (e.target.checked) {
                    setFormats([...formats, format]);
                  } else {
                    setFormats(formats.filter((f) => f !== format));
                  }
                }}
              />
              {format.toUpperCase()}
            </label>
          ))}
        </div>
      </div>
      <div style={{ display: "flex", gap: 16 }}>
        <button
          disabled={!file || status?.state === "running" || formats.length === 0}
          onClick={start}
        >
          {status?.state === "running" ? "Processing..." : "Start"}
        </button>
        {(jobId || status) && (
          <button onClick={reset}>Reset</button>
        )}
      </div>

      {formats.length === 0 && (
        <p style={{ color: "orange" }}>
          Please select at least one output format.
        </p>
      )}

      {status && (
        <div style={{ maxWidth: 600, textAlign: "center" }}>
          <h3>Status: {status.state}</h3>
          {status.step && <p>Step: {status.step}</p>}
          {status.error && <p style={{ color: "red" }}>Error: {status.error}</p>}
          <details>
            <summary>Raw Status</summary>
            <pre style={{ whiteSpace: "pre-wrap", textAlign: "left" }}>
              {JSON.stringify(status, null, 2)}
            </pre>
          </details>
        </div>
      )}

      {status?.state === "done" && (
        <button onClick={download}>Download Artifacts</button>
      )}
    </div>
  );
}

export default App;