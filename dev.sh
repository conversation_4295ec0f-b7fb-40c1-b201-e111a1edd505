#!/usr/bin/env bash
set -euo pipefail

# Launch backend and frontend with auto-reload
# Backend
if ! command -v uv >/dev/null 2>&1; then
  echo "Please install uv: https://docs.astral.sh/uv/getting-started/installation/"
  exit 1
fi

# Ensure ffmpeg exists
if ! command -v ffmpeg >/dev/null 2>&1; then
  echo "ffmpeg not found. Please install ffmpeg."
  exit 1
fi

# Start backend
echo "Starting backend on http://localhost:8080 ..."
(cd backend && uv run uvicorn --host 0.0.0.0 --port 8080 main:app --reload) &
BACK_PID=$!

# Start frontend
if [ -f "frontend/package.json" ]; then
  echo "Starting frontend on http://localhost:5173 ..."
  (cd frontend && npm install && npm run dev) &
  FRONT_PID=$!
else
  echo "No frontend found."
  FRONT_PID=""
fi

cleanup() {
  echo "Stopping dev servers..."
  kill $BACK_PID 2>/dev/null || true
  if [ -n "${FRONT_PID:-}" ]; then
    kill $FRONT_PID 2>/dev/null || true
  fi
}
trap cleanup EXIT

wait
